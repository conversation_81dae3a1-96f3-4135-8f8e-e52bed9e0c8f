#define CL_TARGET_OPENCL_VERSION 300
#include <CL/cl.h>
#include <iostream>
#include <vector>
#include <string>
using namespace std;

#define d 256

const char* kernel_code = R"CLC(
#define d 256
__kernel void rabin_karp_chunked(
    __global const char* text,
    __global const char* pattern,
    const int M,
    const int text_size,
    const int q,
    const int pattern_hash,
    const int h,
    __global int* result,
    const int chunk_size) {

    int chunk_id = get_global_id(0);
    int start = chunk_id * chunk_size;
    int end = min(start + chunk_size + (M - 1), text_size - M + 1);

    if (start >= end) return;

    int t = 0;
    for (int j = 0; j < M; ++j)
        t = (d * t + text[start + j]) % q;

    if (t == pattern_hash) {
        int j;
        for (j = 0; j < M && text[start + j] == pattern[j]; ++j);
        if (j == M) result[start] = 1;
    }

    for (int i = start + 1; i < end; ++i) {
        t = (d * (t - text[i - 1] * h) + text[i + M - 1]) % q;
        if (t < 0) t += q;
        if (t == pattern_hash) {
            int j;
            for (j = 0; j < M && text[i + j] == pattern[j]; ++j);
            if (j == M) result[i] = 1;
        }
    }
}
)CLC";

int rabin_karp_sequence(const string& pat, const string& txt, int q) {
    int M = pat.length(), N = txt.size(), p = 0, t = 0, h = 1, count = 0;   

    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) {
        p = (d * p + pat[i]) % q;
        t = (d * t + txt[i]) % q;
    }

    for (int i = 0; i <= N - M; ++i) {
        if (p == t) {
            int j = 0;
            while (j < M && txt[i + j] == pat[j]) ++j;
            if (j == M) ++count;
        }
        if (i < N - M) {
            t = (d * (t - txt[i] * h) + txt[i + M]) % q;
            if (t < 0) t += q;
        }
    }
    return count;
}

int rabin_karp_opencl_test(const string& pat, const string& txt, cl_device_id device, int q) {
    int M = pat.length(), N = txt.size(), h = 1, p_hash = 0;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) p_hash = (d * p_hash + pat[i]) % q;

    cout << "Pattern: " << pat << endl;
    cout << "Text: " << txt << endl;
    cout << "Pattern hash: " << p_hash << endl;
    cout << "h: " << h << endl;

    const int max_chunks = 8192;
    int chunk_size = max(256, (N - M + max_chunks - 1) / max_chunks);
    size_t num_chunks = (N - M + chunk_size - 1) / chunk_size;

    cout << "Chunk size: " << chunk_size << endl;
    cout << "Num chunks: " << num_chunks << endl;

    cl_context ctx = clCreateContext(nullptr, 1, &device, nullptr, nullptr, nullptr);
    cl_queue_properties props[] = {
     CL_QUEUE_PROPERTIES, CL_QUEUE_PROFILING_ENABLE, 0
    };

    cl_command_queue queue = clCreateCommandQueueWithProperties(
        ctx, device, props, nullptr);

    cl_program prog = clCreateProgramWithSource(ctx, 1, &kernel_code, nullptr, nullptr);
    clBuildProgram(prog, 1, &device, nullptr, nullptr, nullptr);
    cl_kernel kernel = clCreateKernel(prog, "rabin_karp_chunked", nullptr);

    cl_mem txt_buf = clCreateBuffer(ctx, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, N, (void*)txt.data(), nullptr);
    cl_mem pat_buf = clCreateBuffer(ctx, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, M, (void*)pat.data(), nullptr);
    cl_mem res_buf = clCreateBuffer(ctx, CL_MEM_WRITE_ONLY, sizeof(int) * (N - M + 1), nullptr, nullptr);
    vector<int> res(N - M + 1, 0);

    clSetKernelArg(kernel, 0, sizeof(cl_mem), &txt_buf);
    clSetKernelArg(kernel, 1, sizeof(cl_mem), &pat_buf);
    clSetKernelArg(kernel, 2, sizeof(int), &M);
    clSetKernelArg(kernel, 3, sizeof(int), &N);
    clSetKernelArg(kernel, 4, sizeof(int), &q);
    clSetKernelArg(kernel, 5, sizeof(int), &p_hash);
    clSetKernelArg(kernel, 6, sizeof(int), &h);
    clSetKernelArg(kernel, 7, sizeof(cl_mem), &res_buf);
    clSetKernelArg(kernel, 8, sizeof(int), &chunk_size);

    clEnqueueNDRangeKernel(queue, kernel, 1, nullptr, &num_chunks, nullptr, 0, nullptr, nullptr);
    clFinish(queue);

    clEnqueueReadBuffer(queue, res_buf, CL_TRUE, 0, sizeof(int) * res.size(), res.data(), 0, nullptr, nullptr);
    
    int matches = 0;
    for (int i = 0; i < res.size(); ++i) {
        if (res[i] > 0) {
            cout << "Match found at position: " << i << endl;
            matches += res[i];
        }
    }

    clReleaseMemObject(txt_buf);
    clReleaseMemObject(pat_buf);
    clReleaseMemObject(res_buf);
    clReleaseKernel(kernel);
    clReleaseProgram(prog);
    clReleaseCommandQueue(queue);
    clReleaseContext(ctx);

    return matches;
}

int main() {
    // Test với string nhỏ
    string text = "abcabcabcabc";
    string pattern = "abc";
    int q = 101;

    // Get OpenCL device
    cl_uint n;
    clGetPlatformIDs(0, nullptr, &n);
    vector<cl_platform_id> platforms(n);
    clGetPlatformIDs(n, platforms.data(), nullptr);

    vector<cl_device_id> devices;
    for (auto& p : platforms) {
        cl_uint num_dev;
        clGetDeviceIDs(p, CL_DEVICE_TYPE_GPU, 0, nullptr, &num_dev);
        vector<cl_device_id> devs(num_dev);
        clGetDeviceIDs(p, CL_DEVICE_TYPE_GPU, num_dev, devs.data(), nullptr);
        devices.insert(devices.end(), devs.begin(), devs.end());
    }

    if (devices.empty()) {
        cout << "No GPU devices found!" << endl;
        return 1;
    }

    cl_device_id dev = devices[0];

    cout << "=== Sequential Test ===" << endl;
    int seq_result = rabin_karp_sequence(pattern, text, q);
    cout << "Sequential result: " << seq_result << " matches" << endl;

    cout << "\n=== OpenCL Test ===" << endl;
    int opencl_result = rabin_karp_opencl_test(pattern, text, dev, q);
    cout << "OpenCL result: " << opencl_result << " matches" << endl;

    return 0;
}
