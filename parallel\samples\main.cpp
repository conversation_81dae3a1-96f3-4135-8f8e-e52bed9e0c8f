#define CL_TARGET_OPENCL_VERSION 300
#include <CL/cl.h>
#include <omp.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <chrono>
#include <thread>
#include <atomic>
#include <iomanip>
using namespace std;
using namespace std::chrono;

#define d 256


vector<char> fast_load_file(const string& filename) {
    ifstream file(filename, ios::binary | ios::ate);
    if (!file) { cerr << "Cannot open file\n"; exit(1); }
    streamsize size = file.tellg();
    file.seekg(0, ios::beg);
    vector<char> buffer(size);
    if (!file.read(buffer.data(), size)) { cerr << "Error reading file\n"; exit(1); }
    return buffer;
}

int rabin_karp_sequence(const string& pat, const vector<char>& txt, int q) {
    //auto start_time = high_resolution_clock::now();
    int M = pat.length(), N = txt.size(), p = 0, t = 0, h = 1, count = 0;   
    //auto start_sequence_time = high_resolution_clock::now();

    // 2 vòng for tiếp theo không thể song song hóa đ<PERSON>ợc, tuy nhiên thời gian thực thi < 0.1%
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) {
        p = (d * p + pat[i]) % q;
        t = (d * t + txt[i]) % q;
    }
    //auto end_sequence_time = high_resolution_clock::now();
    //double sequence_time = duration_cast<nanoseconds>(end_sequence_time - start_sequence_time).count();

    // vòng for tiếp theo có thể song song hóa được, thời gian thực thi > 99.9%
    auto start_parallel_time = high_resolution_clock::now();
    for (int i = 0; i <= N - M; ++i) {
        if (p == t) {
            int j = 0;
            while (j < M && txt[i + j] == pat[j]) ++j;
            if (j == M) ++count;
        }
        if (i < N - M) {
            t = (d * (t - txt[i] * h) + txt[i + M]) % q;
            if (t < 0) t += q;
        }
    }
    //auto end_parallel_time = high_resolution_clock::now();
    //double parallel_time = duration_cast<nanoseconds>(end_parallel_time - start_parallel_time).count();
    //auto end_time = high_resolution_clock::now();
    //double total_time = duration_cast<nanoseconds>(end_time - start_time).count();
    //cout << "% sequence time: " << sequence_time / total_time * 100 << " %" << endl;
    //cout << "% can be parallelized: " << std::fixed << std::setprecision(9) << (1 - sequence_time / total_time) * 100 << " %" << endl;
    return count;
}

int rabin_karp_omp(const string& pat, const vector<char>& txt, int q, int num_threads) {
    int M = pat.length(), N = txt.size();
    if (M > N) return 0;

    int h = 1;
    for (int i = 0; i < M - 1; ++i)
        h = (h * d) % q;

    int pattern_hash = 0;
    for (int i = 0; i < M; ++i)
        pattern_hash = (d * pattern_hash + pat[i]) % q;

    int count = 0;
    int chunk_size = (N - M + 1 + num_threads - 1) / num_threads;

#pragma omp parallel for reduction(+:count) num_threads(num_threads) schedule(static)
    for (int chunk = 0; chunk < num_threads; ++chunk) {
        int start = chunk * chunk_size;
        int end = min(start + chunk_size + (M - 1), N - M + 1);

        if (start >= end) continue;

        int t = 0;
        for (int j = 0; j < M; ++j)
            t = (d * t + txt[start + j]) % q;

        if (t == pattern_hash) {
            bool match = true;
            for (int j = 0; j < M; ++j) {
                if (txt[start + j] != pat[j]) {
                    match = false;
                    break;
                }
            }
            if (match) ++count;
        }

        for (int i = start + 1; i < end; ++i) {
            t = (d * (t - txt[i - 1] * h) + txt[i + M - 1]) % q;
            if (t < 0) t += q;

            if (t == pattern_hash) {
                int j = 0;
                while (j < M && txt[i + j] == pat[j]) ++j;
                if (j == M) ++count;

            }
        }
    }

    return count;
}

void worker(const string& pat, const vector<char>& txt, int q,
    int start, int end, atomic<int>& count) {
    int M = pat.length();
    int h = 1;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;

    int pattern_hash = 0;
    for (int i = 0; i < M; ++i) pattern_hash = (d * pattern_hash + pat[i]) % q;

    // Rolling hash nội bộ
    int t = 0;
    for (int j = 0; j < M; ++j)
        t = (d * t + txt[start + j]) % q;

    if (t == pattern_hash && memcmp(&txt[start], pat.data(), M) == 0)
        ++count;

    for (int i = start + 1; i < end; ++i) {
        t = (d * (t - txt[i - 1] * h) + txt[i + M - 1]) % q;
        if (t < 0) t += q;

        if (t == pattern_hash && memcmp(&txt[i], pat.data(), M) == 0)
            ++count;
    }
}

// Rabin-Karp song song hóa bằng std::thread
int rabin_karp_threaded(const string& pat, const vector<char>& txt, int q, int num_threads,
    double& compute_time) {
    int M = pat.length(), N = txt.size();
    if (M > N) return 0;

    int chunk_size = (N - M + 1 + num_threads - 1) / num_threads;

    std::atomic<int> count(0);
    vector<thread> threads;

    auto t_start = high_resolution_clock::now();

    for (int t = 0; t < num_threads; ++t) {
        int start = t * chunk_size;
        int end = min(start + chunk_size + (M - 1), N - M + 1); // Đệm M-1 ký tự cho biên

        if (start < end)
            threads.emplace_back(worker, cref(pat), cref(txt), q, start, end, ref(count));
    }

    for (auto& th : threads) th.join();

    auto t_end = high_resolution_clock::now();
    compute_time = duration_cast<duration<double, milli>>(t_end - t_start).count();

    return count;
}

class OpenCLContext {
private:
    cl_context context_;
    cl_command_queue queue_;
    cl_device_id device_;
    
public:
    explicit OpenCLContext(cl_device_id device) : device_(device) {
        cl_int error;
        context_ = clCreateContext(nullptr, 1, &device_, nullptr, nullptr, &error);
        
        cl_queue_properties props[] = {
            CL_QUEUE_PROPERTIES, CL_QUEUE_PROFILING_ENABLE, 0
        };
        queue_ = clCreateCommandQueueWithProperties(context_, device_, props, &error);
    }
    
    ~OpenCLContext() {
        if (queue_) clReleaseCommandQueue(queue_);
        if (context_) clReleaseContext(context_);
    }
    
   

    cl_context getContext() const { return context_; }
    cl_command_queue getQueue() const { return queue_; }
    cl_device_id getDevice() const { return device_; }
};

class OpenCLBuffer {
private:
    cl_mem buffer_;
    
public:
    OpenCLBuffer(cl_context context, cl_mem_flags flags, size_t size, void* host_ptr = nullptr) {
        cl_int error;
        buffer_ = clCreateBuffer(context, flags, size, host_ptr, &error);
    }
    
    ~OpenCLBuffer() {
        if (buffer_) clReleaseMemObject(buffer_);
    }
    
    cl_mem get() const { return buffer_; }
    
    // Prevent copying
    OpenCLBuffer(const OpenCLBuffer&) = delete;
    OpenCLBuffer& operator=(const OpenCLBuffer&) = delete;
    
    // Allow moving
    OpenCLBuffer(OpenCLBuffer&& other) noexcept : buffer_(other.buffer_) {
        other.buffer_ = nullptr;
    }
};

class RabinKarpKernel {
private:
    static constexpr int RADIX = 256;
    static constexpr int MIN_CHUNK_SIZE = 256;
    static constexpr int MAX_CHUNKS = 8192;
    
    cl_program program_;
    cl_kernel kernel_;
    
    static const char* getKernelSource() {
        return R"CLC(
#define d 256
__kernel void rabin_karp_chunked(
    __global const char* text,
    __global const char* pattern,
    const int M,
    const int text_size,
    const int q,
    const int pattern_hash,
    const int h,
    __global int* result,
    const int chunk_size) {

    int chunk_id = get_global_id(0);
    int start = chunk_id * chunk_size;
    int end = min(start + chunk_size + (M - 1), text_size - M + 1);

    if (start >= end) return;

    int t = 0;
    for (int j = 0; j < M; ++j)
        t = (d * t + text[start + j]) % q;

    if (t == pattern_hash) {
        int j;
        for (j = 0; j < M && text[start + j] == pattern[j]; ++j);
        if (j == M) result[start] = 1;
    }

    for (int i = start + 1; i < end; ++i) {
        t = (d * (t - text[i - 1] * h) + text[i + M - 1]) % q;
        if (t < 0) t += q;
        if (t == pattern_hash) {
            int j;
            for (j = 0; j < M && text[i + j] == pattern[j]; ++j);
            if (j == M) result[i] = 1;
        }
    }
}
)CLC";
    }
    
public:
    RabinKarpKernel(const OpenCLContext& context) {
        const char* source = getKernelSource();
        cl_int error;
        
        program_ = clCreateProgramWithSource(context.getContext(), 1, &source, nullptr, &error);
        
        cl_device_id device = context.getDevice();
        error = clBuildProgram(program_, 1, &device, nullptr, nullptr, nullptr);
        if (error != CL_SUCCESS) {
            size_t log_size;
            clGetProgramBuildInfo(program_, context.getDevice(), CL_PROGRAM_BUILD_LOG, 
                                0, nullptr, &log_size);
            std::vector<char> log(log_size);
            clGetProgramBuildInfo(program_, context.getDevice(), CL_PROGRAM_BUILD_LOG, 
                                log_size, log.data(), nullptr);
            throw std::runtime_error("Kernel compilation failed: " + std::string(log.data()));
        }
        
        kernel_ = clCreateKernel(program_, "rabin_karp_chunked", &error);
    }
    
    ~RabinKarpKernel() {
        if (kernel_) clReleaseKernel(kernel_);
        if (program_) clReleaseProgram(program_);
    }
    
    cl_kernel get() const { return kernel_; }
};

class PerformanceTimer {
private:
    std::chrono::high_resolution_clock::time_point start_time_;
    
public:
    void start() {
        start_time_ = std::chrono::high_resolution_clock::now();
    }
    
    double elapsedMilliseconds() const {
        auto end_time = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time_).count() / 1e6;
    }
};

struct PerformanceMetrics {
    double kernel_execution_ms = 0.0;
    double memory_io_ms = 0.0;
    double total_setup_ms = 0.0;
};

struct SearchResult {
    int match_count;
    PerformanceMetrics performance;
};


class RabinKarpSearcher {
private:
    static constexpr int RADIX = 256;
    static constexpr int MIN_CHUNK_SIZE = 256;
    static constexpr int MAX_CHUNKS = 8192;
    
    struct HashParameters {
        int pattern_hash;
        int radix_power;
        int modulus;
    };
    
    HashParameters calculateHashParameters(const std::string& pattern, int modulus) const {
        const int pattern_length = static_cast<int>(pattern.length());
        
        HashParameters params;
        params.modulus = modulus;
        params.radix_power = 1;
        params.pattern_hash = 0;
        
        // Calculate radix^(pattern_length-1) % modulus
        for (int i = 0; i < pattern_length - 1; ++i) {
            params.radix_power = (params.radix_power * RADIX) % modulus;
        }
        
        // Calculate pattern hash
        for (char c : pattern) {
            params.pattern_hash = (RADIX * params.pattern_hash + c) % modulus;
        }
        
        return params;
    }
    
    int calculateOptimalChunkSize(int text_size, int pattern_length) const {
        const int searchable_positions = text_size - pattern_length + 1;
        const int base_chunk_size = std::max(MIN_CHUNK_SIZE, 
            (searchable_positions + MAX_CHUNKS - 1) / MAX_CHUNKS);
        return base_chunk_size;
    }
    
public:
    SearchResult search(const std::string& pattern, 
                       const std::vector<char>& text,
                       cl_device_id device, 
                       int modulus) const {
        
        if (pattern.empty() || text.empty() || pattern.length() > text.size()) {
            return SearchResult{0, PerformanceMetrics{}};
        }
        
        PerformanceTimer total_timer, setup_timer, io_timer;
        total_timer.start();
        setup_timer.start();
        
        const int pattern_length = static_cast<int>(pattern.length());
        const int text_size = static_cast<int>(text.size());
        const int chunk_size = calculateOptimalChunkSize(text_size, pattern_length);
        const size_t num_chunks = (text_size - pattern_length + chunk_size - 1) / chunk_size;
        
        HashParameters hash_params = calculateHashParameters(pattern, modulus);
        
        // Initialize OpenCL resources
        OpenCLContext context(device);
        RabinKarpKernel kernel(context);
        
        // Create buffers
        OpenCLBuffer text_buffer(context.getContext(), 
            CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, 
            text_size, const_cast<char*>(text.data()));
            
        OpenCLBuffer pattern_buffer(context.getContext(), 
            CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, 
            pattern_length, const_cast<char*>(pattern.data()));
            
        const int result_size = text_size - pattern_length + 1;
        OpenCLBuffer result_buffer(context.getContext(), 
            CL_MEM_WRITE_ONLY, sizeof(int) * result_size);
        
        // Set kernel arguments
        setKernelArguments(kernel, text_buffer, pattern_buffer, result_buffer,
                          pattern_length, text_size, hash_params, chunk_size);
        
        PerformanceMetrics metrics;
        metrics.total_setup_ms = setup_timer.elapsedMilliseconds();
        
        // Execute kernel
        metrics.kernel_execution_ms = executeKernel(context, kernel, num_chunks);
        
        // Read results
        io_timer.start();
        std::vector<int> results(result_size, 0);
        cl_int error = clEnqueueReadBuffer(context.getQueue(), result_buffer.get(), 
            CL_TRUE, 0, sizeof(int) * result_size, results.data(), 0, nullptr, nullptr);
        
        int match_count = 0;
        for (int result : results) {
            match_count += result;
        }
        cout << match_count << endl;
        metrics.memory_io_ms = io_timer.elapsedMilliseconds();
        
        return SearchResult{match_count, metrics};
    }
    
private:
    void setKernelArguments(const RabinKarpKernel& kernel,
                           const OpenCLBuffer& text_buffer,
                           const OpenCLBuffer& pattern_buffer,
                           const OpenCLBuffer& result_buffer,
                           int pattern_length, int text_size,
                           const HashParameters& hash_params,
                           int chunk_size) const {

        cl_kernel k = kernel.get();
        cl_mem text_mem = text_buffer.get();
        cl_mem pattern_mem = pattern_buffer.get();
        cl_mem result_mem = result_buffer.get();

        // Kernel parameters: text, pattern, M, text_size, q, pattern_hash, h, result, chunk_size
        clSetKernelArg(k, 0, sizeof(cl_mem), &text_mem);
        clSetKernelArg(k, 1, sizeof(cl_mem), &pattern_mem);
        clSetKernelArg(k, 2, sizeof(int), &pattern_length);  // M
        clSetKernelArg(k, 3, sizeof(int), &text_size);
        clSetKernelArg(k, 4, sizeof(int), &hash_params.modulus);  // q
        clSetKernelArg(k, 5, sizeof(int), &hash_params.pattern_hash);
        clSetKernelArg(k, 6, sizeof(int), &hash_params.radix_power);  // h
        clSetKernelArg(k, 7, sizeof(cl_mem), &result_mem);
        clSetKernelArg(k, 8, sizeof(int), &chunk_size);
    }
    
    double executeKernel(const OpenCLContext& context, 
                        const RabinKarpKernel& kernel, 
                        size_t num_chunks) const {
        cl_event event;
        cl_int error = clEnqueueNDRangeKernel(context.getQueue(), kernel.get(), 1, 
            nullptr, &num_chunks, nullptr, 0, nullptr, &event);
        
        clFinish(context.getQueue());
        
        cl_ulong start_time, end_time;
        clGetEventProfilingInfo(event, CL_PROFILING_COMMAND_START, 
                               sizeof(cl_ulong), &start_time, nullptr);
        clGetEventProfilingInfo(event, CL_PROFILING_COMMAND_END, 
                               sizeof(cl_ulong), &end_time, nullptr);
        
        clReleaseEvent(event);
        return static_cast<double>(end_time - start_time) / 1e6;
    }
};

// Updated public interface
SearchResult rabinKarpSearch(const std::string& pattern, 
                            const std::vector<char>& text,
                            cl_device_id device, 
                            int modulus = 101) {
    RabinKarpSearcher searcher;
    return searcher.search(pattern, text, device, modulus);
}


const char* kernel_code = R"CLC(
#define d 256
__kernel void rabin_karp_chunked(
    __global const char* text,
    __global const char* pattern,
    const int M,
    const int text_size,
    const int q,
    const int pattern_hash,
    const int h,
    __global int* result,
    const int chunk_size) {

    int chunk_id = get_global_id(0);
    int start = chunk_id * chunk_size;
    int end = min(start + chunk_size + (M - 1), text_size - M + 1);

    if (start >= end) return;

    int t = 0;
    for (int j = 0; j < M; ++j)
        t = (d * t + text[start + j]) % q;

    if (t == pattern_hash) {
        int j;
        for (j = 0; j < M && text[start + j] == pattern[j]; ++j);
        if (j == M) result[start] = 1;
    }

    for (int i = start + 1; i < end; ++i) {
        t = (d * (t - text[i - 1] * h) + text[i + M - 1]) % q;
        if (t < 0) t += q;
        if (t == pattern_hash) {
            int j;
            for (j = 0; j < M && text[i + j] == pattern[j]; ++j);
            if (j == M) result[i] = 1;
        }
    }
}
)CLC";

int rabin_karp_opencl(const string& pat, const vector<char>& txt,
    int& matches, double& kernel_ms, double& io_ms, cl_device_id device, int q) {

    int M = pat.length(), N = txt.size(), h = 1, p_hash = 0;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) p_hash = (d * p_hash + pat[i]) % q;

    const int max_chunks = 8192;
    int chunk_size = max(256, (N - M + max_chunks - 1) / max_chunks);
    size_t num_chunks = (N - M + chunk_size - 1) / chunk_size;

    matches = 0;

    auto t1 = high_resolution_clock::now();

    cl_context ctx = clCreateContext(nullptr, 1, &device, nullptr, nullptr, nullptr);
    cl_queue_properties props[] = {
     CL_QUEUE_PROPERTIES, CL_QUEUE_PROFILING_ENABLE, 0
    };

    cl_command_queue queue = clCreateCommandQueueWithProperties(
        ctx, device, props, nullptr);

    cl_program prog = clCreateProgramWithSource(ctx, 1, &kernel_code, nullptr, nullptr);
    clBuildProgram(prog, 1, &device, nullptr, nullptr, nullptr);
    cl_kernel kernel = clCreateKernel(prog, "rabin_karp_chunked", nullptr);

    cl_mem txt_buf = clCreateBuffer(ctx, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, N, (void*)txt.data(), nullptr);
    cl_mem pat_buf = clCreateBuffer(ctx, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, M, (void*)pat.data(), nullptr);
    cl_mem res_buf = clCreateBuffer(ctx, CL_MEM_WRITE_ONLY, sizeof(int) * (N - M + 1), nullptr, nullptr);
    vector<int> res(N - M + 1, 0);

    clSetKernelArg(kernel, 0, sizeof(cl_mem), &txt_buf);
    clSetKernelArg(kernel, 1, sizeof(cl_mem), &pat_buf);
    clSetKernelArg(kernel, 2, sizeof(int), &M);
    clSetKernelArg(kernel, 3, sizeof(int), &N);
    clSetKernelArg(kernel, 4, sizeof(int), &q);
    clSetKernelArg(kernel, 5, sizeof(int), &p_hash);
    clSetKernelArg(kernel, 6, sizeof(int), &h);
    clSetKernelArg(kernel, 7, sizeof(cl_mem), &res_buf);
    clSetKernelArg(kernel, 8, sizeof(int), &chunk_size);

    auto t2 = high_resolution_clock::now();

    cl_event e;
    clEnqueueNDRangeKernel(queue, kernel, 1, nullptr, &num_chunks, nullptr, 0, nullptr, &e);
    clFinish(queue);

    cl_ulong st, en;
    clGetEventProfilingInfo(e, CL_PROFILING_COMMAND_START, sizeof(cl_ulong), &st, nullptr);
    clGetEventProfilingInfo(e, CL_PROFILING_COMMAND_END, sizeof(cl_ulong), &en, nullptr);
    kernel_ms = (en - st) / 1e6;

    auto t3 = high_resolution_clock::now();
    clEnqueueReadBuffer(queue, res_buf, CL_TRUE, 0, sizeof(int) * res.size(), res.data(), 0, nullptr, nullptr);
    for (int x : res) matches += x;
    auto t4 = high_resolution_clock::now();

    io_ms = duration_cast<milliseconds>(t2 - t1).count() + duration_cast<milliseconds>(t4 - t3).count();

    clReleaseEvent(e);
    clReleaseMemObject(txt_buf);
    clReleaseMemObject(pat_buf);
    clReleaseMemObject(res_buf);
    clReleaseKernel(kernel);
    clReleaseProgram(prog);
    clReleaseCommandQueue(queue);
    clReleaseContext(ctx);

    return 0;
}

int main() {
    ios::sync_with_stdio(false); cin.tie(nullptr);

    cout << "Use CPU (0) or GPU (1): ";
    int choice; cin >> choice;
    bool use_cpu = (choice == 0);

    vector<char> txt = fast_load_file("generated_1GB.txt");

    // Đọc tất cả pattern vào vector
    ifstream f("pat.txt");
    vector<string> patterns;
    string pat;
    while (getline(f, pat)) {
        if (!pat.empty()) patterns.push_back(pat);
    }
    f.close();

    int q = 101;
    int threads = 1;
    int sel = 0;
    cl_device_id dev = nullptr;

    // Hỏi một lần số thread hoặc device
    if (use_cpu) {
        cout << "Enter number of threads: ";
        cin >> threads;
    }
    else {
        cl_uint n;
        clGetPlatformIDs(0, nullptr, &n);
        vector<cl_platform_id> platforms(n);
        clGetPlatformIDs(n, platforms.data(), nullptr);

        vector<cl_device_id> devices;
        for (auto& p : platforms) {
            cl_uint num_dev;
            clGetDeviceIDs(p, CL_DEVICE_TYPE_GPU, 0, nullptr, &num_dev);
            vector<cl_device_id> devs(num_dev);
            clGetDeviceIDs(p, CL_DEVICE_TYPE_GPU, num_dev, devs.data(), nullptr);
            devices.insert(devices.end(), devs.begin(), devs.end());
        }

        for (int i = 0; i < devices.size(); ++i) {
            char name[256];
            clGetDeviceInfo(devices[i], CL_DEVICE_NAME, sizeof(name), name, nullptr);
            cout << "[" << i << "] " << name << "\n";
        }
        cout << "Select device index: ";
        cin >> sel;
        dev = devices[sel];
    }

    // Vòng lặp cho mỗi pattern
    for (const string& pat : patterns) {
        cout << "\nPattern: " << pat << endl;

        // Sequence baseline
        cout << "Waiting for calculation sequence..." << endl;
        auto t0 = high_resolution_clock::now();
        int seq_res = rabin_karp_sequence(pat, txt, q);
        auto t1 = high_resolution_clock::now();
        double seq_ms = duration_cast<milliseconds>(t1 - t0).count();
        cout << "Baseline Sequence Result: " << seq_res << ", Time: " << seq_ms << " ms\n";

        if (use_cpu) {
            double compute_time = 0;
            int res = rabin_karp_threaded(pat, txt, q, threads, compute_time);
            cout << "CPU Result: " << res << " matches, Time: " << compute_time << " ms\n";
            cout << "Speedup: " << seq_ms / compute_time << "x\n";
        }
        else {
            int result = 0;
            double kernel_ms = 0, io_ms = 0;
            rabin_karp_opencl(pat, txt, result, kernel_ms, io_ms, dev, q);
            cout << "GPU OpenCL Result: " << result << " matches\n";
            cout << "Kernel Time: " << kernel_ms << " ms, I/O Time: " << io_ms << " ms\n";
            cout << "Speedup (Seq / Kernel): " << seq_ms / kernel_ms << "x\n";

        }
    }
    return 0;
}


