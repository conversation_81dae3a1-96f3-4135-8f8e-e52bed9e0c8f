<svg viewBox="0 0 800 900" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <style>
    .box { fill: white; stroke: #333; stroke-width: 2; }
    .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    .decision { fill: white; stroke: #333; stroke-width: 2; }
    .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
    .small-text { font-family: Arial, sans-serif; font-size: 12px; }
  </style>
  
  <!-- Arrow marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Start node -->
  <ellipse cx="400" cy="40" rx="80" ry="30" class="box" />
  <text x="400" y="45" class="text"><PERSON><PERSON><PERSON> đầu</text>
  
  <!-- Input -->
  <rect x="300" y="90" width="200" height="60" rx="5" class="box" />
  <text x="400" y="125" class="text">Nhập chuỗi văn bản T và</text>
  <text x="400" y="145" class="text">chuỗi mẫu P</text>
  
  <!-- Initialize -->
  <rect x="300" y="180" width="200" height="80" rx="5" class="box" />
  <text x="400" y="205" class="text">Khởi tạo:</text>
  <text x="400" y="225" class="text">n = độ dài T</text>
  <text x="400" y="245" class="text">m = độ dài P</text>
  
  <!-- Calculate hash -->
  <rect x="300" y="290" width="200" height="80" rx="5" class="box" />
  <text x="400" y="315" class="text">Tính giá trị hash p_hash</text>
  <text x="400" y="335" class="text">cho chuỗi mẫu P</text>
  <text x="400" y="355" class="text">và t_hash cho T[0...m-1]</text>
  
  <!-- For loop -->
  <rect x="300" y="400" width="200" height="60" rx="5" class="box" />
  <text x="400" y="425" class="text">Duyệt i từ 0 đến n-m</text>
  <text x="400" y="445" class="text">(vị trí bắt đầu có thể khớp)</text>
  
  <!-- Hash check -->
  <polygon points="400,490 500,550 400,610 300,550" class="decision" />
  <text x="400" y="540" class="text">t_hash == p_hash?</text>
  <text x="400" y="560" class="text">(hash khớp?)</text>
  
  <!-- Character check -->
  <rect x="550" y="520" width="200" height="60" rx="5" class="box" />
  <text x="650" y="545" class="text">Kiểm tra từng ký tự</text>
  <text x="650" y="565" class="text">T[i...i+m-1] với P[0...m-1]</text>
  
  <!-- Pattern found -->
  <polygon points="650,610 750,670 650,730 550,670" class="decision" />
  <text x="650" y="660" class="text">Tất cả ký tự</text>
  <text x="650" y="680" class="text">đều khớp?</text>
  
  <!-- Found output -->
  <rect x="550" y="760" width="200" height="60" rx="5" class="box" />
  <text x="650" y="790" class="text">Thêm vị trí i vào</text>
  <text x="650" y="810" class="text">danh sách kết quả</text>
  
  <!-- Update hash -->
  <rect x="140" y="520" width="200" height="60" rx="5" class="box" />
  <text x="240" y="545" class="text">Cập nhật t_hash cho</text>
  <text x="240" y="565" class="text">cửa sổ tiếp theo T[i+1...i+m]</text>
  
  <!-- End node -->
  <ellipse cx="400" cy="850" rx="80" ry="30" class="box" />
  <text x="400" y="855" class="text">Kết thúc</text>
  
  <!-- Arrows -->
  <path d="M400,70 L400,90" class="arrow" />
  <path d="M400,150 L400,180" class="arrow" />
  <path d="M400,260 L400,290" class="arrow" />
  <path d="M400,370 L400,400" class="arrow" />
  <path d="M400,460 L400,490" class="arrow" />
  
  <!-- Decision arrows -->
  <path d="M500,550 L550,550" class="arrow" />
  <text x="525" y="540" class="small-text">Có</text>
  
  <path d="M300,550 L240,550" class="arrow" />
  <text x="270" y="540" class="small-text">Không</text>
  
  <path d="M650,730 L650,760" class="arrow" />
  <text x="670" y="745" class="small-text">Có</text>
  
  <path d="M550,670 L240,670 L240,580" class="arrow" />
  <text x="520" y="660" class="small-text">Không</text>

  <path d="M650,580 L650,610" class="arrow" />
  
  <!-- Loop back or continue -->
  <path d="M240,520 L240,430 L300,430" class="arrow" />
  <path d="M650,820 L650,850 L480,850" class="arrow" />
  
  <!-- Final path -->
  <path d="M240,670 L240,850 L320,850" class="arrow" />
  <text x="280" y="840" class="small-text">Hết vòng lặp</text>
</svg>