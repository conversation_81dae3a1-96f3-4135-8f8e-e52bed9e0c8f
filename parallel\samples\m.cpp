﻿#define CL_TARGET_OPENCL_VERSION 300
#include <CL/cl.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <chrono>
#include <windows.h>

using namespace std;
using namespace std::chrono;

#define d 256

void limit_cpu_cores(int num_cores) {
    DWORD_PTR affinity_mask = 0;
    for (int i = 0; i < num_cores; ++i)
        affinity_mask |= (1ull << i);
    SetProcessAffinityMask(GetCurrentProcess(), affinity_mask);
}

vector<char> fast_load_file(const string& filename) {
    printf("Attempting to open file: %s\n", filename.c_str());
    fflush(stdout);

    ifstream file(filename, ios::binary | ios::ate);
    if (!file) {
        fprintf(stderr, "Cannot open file: %s\n", filename.c_str());
        fprintf(stderr, "Make sure the file exists in the current directory.\n");
        exit(1);
    }

    streamsize size = file.tellg();
    printf("File size: %lld bytes (%lld MB)\n", (long long)size, (long long)size / (1024*1024));
    fflush(stdout);

    file.seekg(0, ios::beg);
    vector<char> buffer(size);
    printf("Reading file into memory...\n");
    fflush(stdout);

    if (!file.read(buffer.data(), size)) {
        fprintf(stderr, "Error reading file: %s\n", filename.c_str());
        exit(1);
    }

    printf("File loaded successfully!\n");
    return buffer;
}

const char* kernel_code = R"CLC(
#define d 256
__kernel void rabin_karp_kernel(
    __global const char* text,
    __global const char* pattern,
    const int M,
    const int text_size,
    const int q,
    const int pattern_hash,
    const int h,
    __global int* result) {

    int i = get_global_id(0);
    result[i] = 0;

    if (i + M > text_size) return;

    int t = 0;
    for (int j = 0; j < M; ++j) {
        t = (d * t + text[i + j]) % q;
    }

    if (t == pattern_hash) {
        for (int j = 0; j < M; ++j) {
            if (text[i + j] != pattern[j]) return;
        }
        result[i] = 1;
    }
}
)CLC";

cl_device_id select_device() {
    printf("Initializing OpenCL...\n");
    fflush(stdout);

    cl_uint num_platforms;
    cl_int err = clGetPlatformIDs(0, nullptr, &num_platforms);
    if (err != CL_SUCCESS || num_platforms == 0) {
        fprintf(stderr, "Error: No OpenCL platforms found! Error code: %d\n", err);
        exit(1);
    }

    vector<cl_platform_id> platforms(num_platforms);
    clGetPlatformIDs(num_platforms, platforms.data(), nullptr);

    vector<cl_device_id> all_devices;
    printf("Found %u OpenCL platform(s)\n", num_platforms);

    for (auto plat : platforms) {
        cl_uint num_devices;
        clGetDeviceIDs(plat, CL_DEVICE_TYPE_ALL, 0, nullptr, &num_devices);
        if (num_devices > 0) {
            vector<cl_device_id> devices(num_devices);
            clGetDeviceIDs(plat, CL_DEVICE_TYPE_ALL, num_devices, devices.data(), nullptr);
            all_devices.insert(all_devices.end(), devices.begin(), devices.end());
        }
    }

    if (all_devices.empty()) {
        fprintf(stderr, "Error: No OpenCL devices found!\n");
        exit(1);
    }

    printf("Available devices:\n");
    for (int i = 0; i < all_devices.size(); ++i) {
        char name[256];
        cl_device_type type;
        clGetDeviceInfo(all_devices[i], CL_DEVICE_NAME, sizeof(name), name, nullptr);
        clGetDeviceInfo(all_devices[i], CL_DEVICE_TYPE, sizeof(type), &type, nullptr);

        const char* type_str = (type == CL_DEVICE_TYPE_CPU) ? "CPU" :
                              (type == CL_DEVICE_TYPE_GPU) ? "GPU" : "Other";
        printf("[%d] %s (%s)\n", i, name, type_str);
    }

    int selection;
    printf("Select device index: ");
    fflush(stdout);
    scanf("%d", &selection);

    if (selection < 0 || selection >= (int)all_devices.size()) {
        fprintf(stderr, "Invalid selection.\n");
        exit(1);
    }

    char selected_name[256];
    clGetDeviceInfo(all_devices[selection], CL_DEVICE_NAME, sizeof(selected_name), selected_name, nullptr);
    printf("Selected device: %s\n", selected_name);

    return all_devices[selection];
}

int rabin_karp_sequence(const string& pat, const vector<char>& txt, int q) {
    int M = pat.length(), N = txt.size(), p = 0, t = 0, h = 1, count = 0;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) {
        p = (d * p + pat[i]) % q;
        t = (d * t + txt[i]) % q;
    }
    for (int i = 0; i <= N - M; ++i) {
        if (p == t) {
            int j = 0;
            while (j < M && txt[i + j] == pat[j]) ++j;
            if (j == M) ++count;
        }
        if (i < N - M) {
            t = (d * (t - txt[i] * h) + txt[i + M]) % q;
            if (t < 0) t += q;
        }
    }
    return count;
}

int rabin_karp_parallel(const string& pat, const vector<char>& txt, int& total_matches, double& kernel_only_ms, cl_device_id device) {
    int M = pat.length();
    int N = txt.size();
    int q = 65521;
    int pattern_hash = 0, h = 1;
    for (int i = 0; i < M - 1; ++i) h = (h * d) % q;
    for (int i = 0; i < M; ++i) pattern_hash = (d * pattern_hash + pat[i]) % q;
    size_t work_items = N >= M ? N - M + 1 : 0;
    total_matches = 0;

    cl_device_type device_type;
    clGetDeviceInfo(device, CL_DEVICE_TYPE, sizeof(device_type), &device_type, nullptr);
    bool is_cpu = (device_type == CL_DEVICE_TYPE_CPU);

    cl_context context = clCreateContext(nullptr, 1, &device, nullptr, nullptr, nullptr);
    cl_queue_properties props[] = { CL_QUEUE_PROPERTIES, CL_QUEUE_PROFILING_ENABLE, 0 };
    cl_command_queue queue = clCreateCommandQueueWithProperties(context, device, props, nullptr);
    cl_program program = clCreateProgramWithSource(context, 1, &kernel_code, nullptr, nullptr);
    clBuildProgram(program, 1, &device, nullptr, nullptr, nullptr);
    cl_kernel kernel = clCreateKernel(program, "rabin_karp_kernel", nullptr);

    cl_mem txt_buf, pat_buf;
    if (is_cpu) {
        txt_buf = clCreateBuffer(context, CL_MEM_READ_ONLY | CL_MEM_USE_HOST_PTR, txt.size(), (void*)txt.data(), nullptr);
        pat_buf = clCreateBuffer(context, CL_MEM_READ_ONLY | CL_MEM_USE_HOST_PTR, M, (void*)pat.data(), nullptr);
    }
    else {
        txt_buf = clCreateBuffer(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, txt.size(), (void*)txt.data(), nullptr);
        pat_buf = clCreateBuffer(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, M, (void*)pat.data(), nullptr);
    }

    cl_mem result_buf = clCreateBuffer(context, CL_MEM_WRITE_ONLY, sizeof(int) * work_items, nullptr, nullptr);
    vector<int> results(work_items);

    clSetKernelArg(kernel, 0, sizeof(cl_mem), &txt_buf);
    clSetKernelArg(kernel, 1, sizeof(cl_mem), &pat_buf);
    clSetKernelArg(kernel, 2, sizeof(int), &M);
    clSetKernelArg(kernel, 3, sizeof(int), &N);
    clSetKernelArg(kernel, 4, sizeof(int), &q);
    clSetKernelArg(kernel, 5, sizeof(int), &pattern_hash);
    clSetKernelArg(kernel, 6, sizeof(int), &h);
    clSetKernelArg(kernel, 7, sizeof(cl_mem), &result_buf);

    size_t global_work_size = work_items;
    cl_event kernel_event;
    clEnqueueNDRangeKernel(queue, kernel, 1, nullptr, &global_work_size, nullptr, 0, nullptr, &kernel_event);
    clFinish(queue);

    cl_ulong start_time, end_time;
    clGetEventProfilingInfo(kernel_event, CL_PROFILING_COMMAND_START, sizeof(cl_ulong), &start_time, nullptr);
    clGetEventProfilingInfo(kernel_event, CL_PROFILING_COMMAND_END, sizeof(cl_ulong), &end_time, nullptr);
    kernel_only_ms = (end_time - start_time) / 1e6;

    clEnqueueReadBuffer(queue, result_buf, CL_TRUE, 0, sizeof(int) * work_items, results.data(), 0, nullptr, nullptr);
    for (int i = 0; i < work_items; ++i) total_matches += results[i];

    clReleaseEvent(kernel_event);
    clReleaseMemObject(txt_buf);
    clReleaseMemObject(pat_buf);
    clReleaseMemObject(result_buf);
    clReleaseKernel(kernel);
    clReleaseProgram(program);
    clReleaseCommandQueue(queue);
    clReleaseContext(context);

    return 0;
}

int main() {
    // Đảm bảo output hiển thị ngay lập tức
    setvbuf(stdout, NULL, _IONBF, 0);
    setvbuf(stderr, NULL, _IONBF, 0);

    printf("=== Rabin-Karp OpenCL Implementation ===\n");
    printf("Enter number of CPU cores to use: ");
    fflush(stdout);

    int num_cpu_cores = 8;
    scanf("%d", &num_cpu_cores);
    printf("Using %d CPU cores\n", num_cpu_cores);
    limit_cpu_cores(num_cpu_cores);

    printf("Selecting OpenCL device...\n");
    fflush(stdout);
    cl_device_id device = select_device();

    printf("Loading text file (this may take a while for large files)...\n");
    fflush(stdout);
    vector<char> txt = fast_load_file("generated_1GB.txt");
    printf("File loaded successfully!\n");

    ifstream patFile("pat.txt");
    string pat;
    while (getline(patFile, pat)) {
        int q = 65521;

        /*auto start_cpu = high_resolution_clock::now();
        int result_cpu = rabin_karp_sequence(pat, txt, q);
        auto end_cpu = high_resolution_clock::now();*/

        int result_gpu = 0;
        double kernel_time_ms = 0.0;
        auto start_gpu = high_resolution_clock::now();
        rabin_karp_parallel(pat, txt, result_gpu, kernel_time_ms, device);
        auto end_gpu = high_resolution_clock::now();

        // double cpu_ms = duration_cast<milliseconds>(end_cpu - start_cpu).count();
        // double speedup = cpu_ms / kernel_time_ms;

        cout << "Pattern: \"" << pat << "\"\n";
        // cout << "  Sequence: " << result_cpu << " matches, Time: " << cpu_ms << " ms\n";
        cout << "  Parallel: " << result_gpu << " matches, Total: "
            << duration_cast<milliseconds>(end_gpu - start_gpu).count()
            << " ms, Kernel-only: " << kernel_time_ms << " ms\n";
        // cout << "  Speedup (Seq / Kernel): " << speedup << "x\n";
    }
    patFile.close();
    return 0;
}
